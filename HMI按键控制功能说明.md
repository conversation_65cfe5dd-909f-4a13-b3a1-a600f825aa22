# HMI按键控制功能说明

## 功能概述
实现了HMI触摸屏按键控制的数据显示功能，按键按下前只收集数据不显示，按键按下后使用平滑算法显示数据。

## HMI按键配置
- **按键事件**: `printh 40 31 55;`
- **发送数据格式**: `@1\n` (包头@，数据1，包尾\n)
- **串口**: 通过串口2发送到STM32

## 工作流程

### 1. 数据收集阶段（按键未按下）
- 摄像头发送的所有数据（@DISTANCE, @TRIANGLE, @CIRCLE, @SQUARE, #数据等）
- 只保存到 `latest_distance` 和 `latest_x` 变量中
- **不更新HMI显示**
- 串口1输出提示："收集XXX数据: XX.XX (等待按键)"

### 2. 按键按下处理
- HMI发送 `@1\n` 到串口2
- STM32接收并识别按键信号
- 调用 `handle_button_press()` 函数
- 使用平滑算法处理数据
- 更新HMI显示

### 3. 数据平滑算法
```c
float smooth_data(float current_display, float new_data)
{
    return current_display + SMOOTH_FACTOR * (new_data - current_display);
}
```
- **SMOOTH_FACTOR = 0.3f**: 平滑因子，值越小变化越平缓
- 使用指数移动平均避免显示跳变过大

## 关键变量

### 状态控制
- `button_pressed`: 按键状态标志 (0=未按下, 1=已按下)

### 数据存储
- `latest_distance`: 最新收集的距离数据
- `latest_x`: 最新收集的x数据
- `displayed_distance`: 当前显示的距离（平滑后）
- `displayed_x`: 当前显示的x值（平滑后）

### 兼容性
- 保持对原有数据格式的完全兼容
- 支持 @DISTANCE:, @TRIANGLE:, @CIRCLE:, @SQUARE:, @123.45, #123.45 等格式

## HMI显示更新

### 距离显示 (index.x2)
- 只有按键按下且 `displayed_distance > 0.0f` 时更新
- 数值乘100后转为整数发送

### X值显示 (index.x3)  
- 只有按键按下且 `displayed_x != 0.0f` 时更新
- 数值乘100后转为整数发送

## 调试信息
- 串口1输出详细的数据收集和处理信息
- 包含平滑前后的数值对比
- HMI按键事件的确认信息

## 使用方法
1. 启动系统，摄像头开始发送数据
2. 系统收集数据但不显示到HMI
3. 按下HMI上的"开始"按键
4. 系统使用平滑算法显示收集到的数据
5. 可重复按键获取新的平滑显示结果
