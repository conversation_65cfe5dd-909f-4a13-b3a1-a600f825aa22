# 🎯 HMI按键控制功能实现说明

## 📋 功能概述

根据你的需求，我已经为代码添加了HMI按键控制功能，实现以下特性：

1. **按键前数据收集**：按键未按下时，只收集D和x数据到缓存，不更新显示
2. **按键触发平均值计算**：按键按下后，计算缓存中数据的平均值并显示
3. **串口2按键数据接收**：接收HMI发送的按键数据（格式：@1\n）

## 🔧 主要修改

### 1. 新增变量定义（第27-35行）
```c
// 按键控制和数据缓存相关变量
uint8_t button_pressed = 0;  // 按键状态标志：0=未按下，1=已按下
#define BUFFER_SIZE 10       // 缓存大小
float distance_buffer[BUFFER_SIZE];  // 距离数据缓存
float x_buffer[BUFFER_SIZE];         // x数据缓存
uint8_t distance_buffer_count = 0;   // 距离缓存计数
uint8_t x_buffer_count = 0;          // x缓存计数
float avg_distance = 0.0f;           // 平均距离
float avg_x = 0.0f;                  // 平均x值
```

### 2. 数据缓存管理函数（第37-95行）
- `add_distance_sample(float distance)` - 添加距离数据到缓存
- `add_x_sample(float x_value)` - 添加x数据到缓存
- `get_distance_average(void)` - 计算距离平均值
- `get_x_average(void)` - 计算x平均值
- `handle_button_press(void)` - 处理按键按下事件

### 3. 修改UpdateHMIDisplay函数（第109-148行）
```c
// 只有按键按下后才更新显示
if (button_pressed) {
    // 更新距离显示（使用平均值）
    if (avg_distance > 0.0f) {
        int distance_int = (int)(avg_distance * 100);
        sprintf(hmi_cmd, "index.x2.val=%d\xff\xff\xff", distance_int);
        Serial2_SendString(hmi_cmd);
        // ...
    }
    
    // 更新x显示（使用平均值）
    if (avg_x > 0.0f) {
        int x_int = (int)(avg_x * 100);
        sprintf(hmi_cmd, "index.x3.val=%d\xff\xff\xff", x_int);
        Serial2_SendString(hmi_cmd);
        // ...
    }
} else {
    // 按键未按下时，只收集数据不更新显示
    sprintf(hmi_cmd, "数据收集中，等待按键按下...\r\n");
    Serial_SendString(hmi_cmd);
}
```

### 4. 修改数据接收处理逻辑
所有@和#数据的处理都改为：
```c
// 添加到缓存
add_distance_sample(current_distance);  // 距离数据
add_x_sample(x_value);                  // x数据

// 只有按键按下后才更新显示
if (button_pressed) {
    UpdateHMIDisplay();
}
```

### 5. 新增串口2按键数据接收（第399-428行）
```c
// 检查串口2接收（HMI按键数据）
if(Serial2_GetLineFlag() == 1) {
    char* hmi_data = Serial2_GetLineData();
    
    // 处理HMI按键发送的数据（格式：@1\n）
    if (hmi_data_length > 0 && hmi_data[0] == '@') {
        if (hmi_data[1] == '1') {
            Serial_SendString("检测到按键按下信号！\r\n");
            handle_button_press();
        }
    }
}
```

## 🚀 工作流程

### 数据收集阶段（按键未按下）
```
串口1接收数据 → 解析@/#数据 → 添加到缓存 → 不更新显示 → 继续收集
```

### 按键按下处理
```
HMI按键 → 发送@1\n → 串口2接收 → 计算平均值 → 更新显示屏
```

## 📊 HMI界面配置

### 按键设置
- **按键事件**：`printh 40 31 55;`
- **数据格式**：`@1\n`（包头@，数据1，包尾\n）

### 显示控件
- **距离D**：`index.x2.val` - 显示平均距离×100
- **X**：`index.x3.val` - 显示平均x值×100

## 🔍 调试信息

系统会输出详细的调试信息：
- 数据接收和解析状态
- 缓存添加情况
- 按键检测状态
- 平均值计算结果
- 显示更新状态

## ✅ 功能特点

- ✅ **智能数据收集**：按键前只收集不显示
- ✅ **误差减小**：通过平均值计算减少测量误差
- ✅ **缓存管理**：10个数据的滑动窗口缓存
- ✅ **实时响应**：按键按下立即计算并显示
- ✅ **完整调试**：详细的状态监控信息
- ✅ **兼容性**：支持所有现有的@和#数据格式

现在你的系统可以完美实现按键控制的数据采集和平均值显示功能！
