#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "Serial.h"
#include "ADC.h"
#include "Serial.h"
#include <string.h>
#include <stdio.h>

// 函数声明
void UpdateHMIDisplay(void);
void add_distance_sample(float distance);
void add_x_sample(float x_value);
float get_distance_average(void);
float get_x_average(void);
void handle_button_press(void);
#define USART1_RX_BUFFER_SIZE 64
char usart1_rx_line_buffer[USART1_RX_BUFFER_SIZE];
uint8_t usart1_rx_line_idx = 0;
volatile uint8_t usart1_line_received_flag = 0;
float temp_D_float_val=0.0f;

uint8_t temp_X;
uint8_t parsed_D_value;
float parsed_D_value_float;
int parsed_X_value = 0;

// 简化：直接存储和显示测量数据
float current_distance = 0.0f;
float current_shape_size = 0.0f;
char current_shape_type[20] = "NONE";

// 新增：x变量用于存储#数据
float x = 0.0f;

// 按键控制和数据缓存相关变量
uint8_t button_pressed = 0;  // 按键状态标志：0=未按下，1=已按下
#define BUFFER_SIZE 10       // 缓存大小
float distance_buffer[BUFFER_SIZE];  // 距离数据缓存
float x_buffer[BUFFER_SIZE];         // x数据缓存
uint8_t distance_buffer_count = 0;   // 距离缓存计数
uint8_t x_buffer_count = 0;          // x缓存计数
float avg_distance = 0.0f;           // 平均距离
float avg_x = 0.0f;                  // 平均x值

// 添加距离数据到缓存
void add_distance_sample(float distance)
{
    if (distance_buffer_count < BUFFER_SIZE) {
        distance_buffer[distance_buffer_count] = distance;
        distance_buffer_count++;
    } else {
        // 缓存满了，移动数据并添加新数据
        for (int i = 0; i < BUFFER_SIZE - 1; i++) {
            distance_buffer[i] = distance_buffer[i + 1];
        }
        distance_buffer[BUFFER_SIZE - 1] = distance;
    }
}

// 添加x数据到缓存
void add_x_sample(float x_value)
{
    if (x_buffer_count < BUFFER_SIZE) {
        x_buffer[x_buffer_count] = x_value;
        x_buffer_count++;
    } else {
        // 缓存满了，移动数据并添加新数据
        for (int i = 0; i < BUFFER_SIZE - 1; i++) {
            x_buffer[i] = x_buffer[i + 1];
        }
        x_buffer[BUFFER_SIZE - 1] = x_value;
    }
}

// 计算距离平均值
float get_distance_average(void)
{
    if (distance_buffer_count == 0) return 0.0f;

    float sum = 0.0f;
    for (int i = 0; i < distance_buffer_count; i++) {
        sum += distance_buffer[i];
    }
    return sum / distance_buffer_count;
}

// 计算x平均值
float get_x_average(void)
{
    if (x_buffer_count == 0) return 0.0f;

    float sum = 0.0f;
    for (int i = 0; i < x_buffer_count; i++) {
        sum += x_buffer[i];
    }
    return sum / x_buffer_count;
}

// 处理按键按下事件
void handle_button_press(void)
{
    button_pressed = 1;

    // 计算平均值
    avg_distance = get_distance_average();
    avg_x = get_x_average();

    char debug_buf[200];
    sprintf(debug_buf, "按键按下！计算平均值 - 距离: %.2f (样本数: %d), X: %.2f (样本数: %d)\r\n",
            avg_distance, distance_buffer_count, avg_x, x_buffer_count);
    Serial_SendString(debug_buf);

    // 立即更新显示
    UpdateHMIDisplay();
}

// 简化的HMI更新函数 - 根据按键状态显示数据
void UpdateHMIDisplay(void)
{
    char hmi_cmd[100];

    // 只有按键按下后才更新显示
    if (button_pressed) {
        // 更新距离显示（使用平均值）
        if (avg_distance > 0.0f) {
            int distance_int = (int)(avg_distance * 100);

            sprintf(hmi_cmd, "index.x2.val=%d\xff\xff\xff", distance_int);
            Serial2_SendString(hmi_cmd);
            Delay_ms(10);
            Serial2_SendString("ref index.x2\xff\xff\xff");
            Delay_ms(10);

            sprintf(hmi_cmd, "距离平均值显示: %.2f cm\r\n", avg_distance);
            Serial_SendString(hmi_cmd);
        }

        // 更新x显示（使用平均值）
        if (avg_x > 0.0f) {
            int x_int = (int)(avg_x * 100);  // x值乘100后强制转为整形

            sprintf(hmi_cmd, "index.x3.val=%d\xff\xff\xff", x_int);
            Serial2_SendString(hmi_cmd);
            Delay_ms(10);
            Serial2_SendString("ref index.x3\xff\xff\xff");
            Delay_ms(10);

            sprintf(hmi_cmd, "X平均值显示: %.2f (整数值: %d)\r\n", avg_x, x_int);
            Serial_SendString(hmi_cmd);
        }
    } else {
        // 按键未按下时，只收集数据不更新显示
        sprintf(hmi_cmd, "数据收集中，等待按键按下...\r\n");
        Serial_SendString(hmi_cmd);
    }
}

uint8_t RxData;			//定义用于接收串口数据的变量
uint8_t old_x;  // 重命名以避免与新的float x变量冲突
float I;
float P;
uint8_t D;
uint16_t ADValue;			//定义AD值变量
float Voltage;
float A;
char debug_serial1_buf[100];



void HandleUsart1RxByte(uint8_t rx_byte)
{
    // 确保缓冲区不会溢出
    if (usart1_rx_line_idx < USART1_RX_BUFFER_SIZE - 1) // 留一个位置给字符串结束符 '\0'
    {
        if (rx_byte == '\n' || rx_byte == '\r') // 检测到行结束符 (换行符或回车符)
        {
            if (usart1_rx_line_idx > 0) // 确保在行结束符之前有实际数据
            {
                usart1_rx_line_buffer[usart1_rx_line_idx] = '\0'; // 字符串以 null 结尾
                usart1_line_received_flag = 1;                   // 设置标志，通知主循环有完整行数据待处理
                Serial_SendString(">>> Line complete flag set! <<<\r\n"); // 调试信息
            }
            // 不重置索引，让主循环处理完后再重置
        }
        else if (rx_byte >= 32 && rx_byte <= 126) // 只接收可打印字符
        {
            usart1_rx_line_buffer[usart1_rx_line_idx++] = rx_byte; // 将接收到的字节存入缓冲区
        }
        // 忽略其他控制字符
    }
    else // 缓冲区已满，重置以防止溢出，并丢弃当前行（可选：可以添加错误处理）
    {
        usart1_rx_line_idx = 0;
        Serial_SendString("USART1 RX Buffer Overflow!\r\n"); // 调试信息
    }
}

int main(void)
{
	/*系统初始化延时*/
	Delay_ms(100); //等待系统稳定

	/*禁用JTAG，释放PB3、PB4、PA15引脚*/
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE); //开启AFIO时钟
	GPIO_PinRemapConfig(GPIO_Remap_SWJ_JTAGDisable, ENABLE); //禁用JTAG，保留SWD

	/*串口初始化*/
	Serial_Init(); //串口初始化
	Delay_ms(50); //初始化间隔

	// 初始化串口2用于与串口屏通信
	Serial2_Init();
	Delay_ms(10); //初始化间隔
	// AD_Init(); //ADC初始化 - 暂时注释掉

	char String[100];
	uint32_t counter = 0; //计数器用于连续发送

	Delay_ms(100); //等待初始化完成
	Serial_SendString("STM32 Started!\r\n");
	Serial_SendString("System Clock: 8MHz HSI\r\n");
	Serial_SendString("JTAG Disabled, SWD Enabled\r\n");
	Serial_SendString("Waiting for Camera Data on USART1...\r\n");
	Serial_SendString("Supported formats:\r\n");
	Serial_SendString("  @DISTANCE:xx.xx -> index.x2.val (距离D)\r\n");
	Serial_SendString("  @TRIANGLE:xx.xx -> index.x3.val (X)\r\n");
	Serial_SendString("  @CIRCLE:xx.xx   -> index.x3.val (X)\r\n");
	Serial_SendString("  @SQUARE:xx.xx   -> index.x3.val (X)\r\n");
	Serial_SendString("  @NO_DETECTION:0.00 -> clear display\r\n");
	Serial_SendString("  @123.45 (legacy) -> index.x2.val (距离D)\r\n");

	// 清零显示
	Serial_SendString("初始化显示屏...\r\n");
	Serial2_SendString("index.x2.val=0\xff\xff\xff");
	Delay_ms(100);
	Serial2_SendString("index.x3.val=0\xff\xff\xff");
	Delay_ms(100);

	Serial_SendString("=== 系统就绪，等待摄像头数据 ===\r\n");

	while (1)
	{
		// 简单的心跳 - 每10000次循环显示一次状态
		if(counter % 10000 == 0) {
			sprintf(String, "系统运行中... 距离: %.2f cm, %s: %.2f cm\r\n",
			        current_distance, current_shape_type, current_shape_size);
			Serial_SendString(String);
		}

		counter++;

		// 暂时注释掉ADC相关代码
		// ADValue = AD_GetValue(); //获取AD转换的值
		// Voltage = (float)ADValue / 4095 * 3.3;
		// A = Voltage*10/0.5; //计算电流
		// sprintf(String, "ADC: %d, Voltage: %.2fV, Current: %.2fA\r\n", ADValue, Voltage, A);
		// Serial_SendString(String);

		// 移除HMI按键处理 - 直接显示数据

		// 检查串口接收 - 使用中断处理的结果
		if(Serial_GetLineFlag() == 1) // 检查是否接收到完整行
		{
			usart1_line_received_flag = 0; // 清除标志位，表示已处理
			usart1_rx_line_idx = 0; // 重置索引，准备接收下一行
			Serial_SendString("=== RX from Camera ===\r\n");
			Serial_SendString("Raw data: ");
			char* line_data = Serial_GetLineData(); // 获取中断处理的数据
			Serial_SendString(line_data); // 打印整个接收缓冲区内容
			Serial_SendString("\r\n");
			uint8_t data_length = strlen(line_data);
			sprintf(debug_serial1_buf, "Data length: %d\r\n", data_length);
			Serial_SendString(debug_serial1_buf);

			// 处理摄像头发送的数据
			if (data_length > 0)
			{
				// 处理以@开头的摄像头数据（如@DISTANCE:123.45）
				if (line_data[0] == '@')
				{
					// 解析带类型标识的数据
					if (strncmp(&line_data[1], "DISTANCE:", 9) == 0)
					{
						if (sscanf(&line_data[10], "%f", &current_distance) == 1)
						{
							strcpy(current_shape_type, "距离");
							sprintf(debug_serial1_buf, "接收距离数据: %.2f cm\r\n", current_distance);
							Serial_SendString(debug_serial1_buf);

							// 添加到缓存
							add_distance_sample(current_distance);

							// 只有按键按下后才更新显示
							if (button_pressed) {
								UpdateHMIDisplay();
							}
						}
					}
					else if (strncmp(&line_data[1], "TRIANGLE:", 9) == 0)
					{
						if (sscanf(&line_data[10], "%f", &current_shape_size) == 1)
						{
							strcpy(current_shape_type, "三角形");
							sprintf(debug_serial1_buf, "接收三角形数据: %.2f cm\r\n", current_shape_size);
							Serial_SendString(debug_serial1_buf);

							// 添加到x缓存（图形尺寸数据）
							add_x_sample(current_shape_size);

							// 只有按键按下后才更新显示
							if (button_pressed) {
								UpdateHMIDisplay();
							}
						}
					}
					else if (strncmp(&line_data[1], "CIRCLE:", 7) == 0)
					{
						if (sscanf(&line_data[8], "%f", &current_shape_size) == 1)
						{
							strcpy(current_shape_type, "圆形");
							sprintf(debug_serial1_buf, "接收圆形数据: 直径 %.2f cm\r\n", current_shape_size);
							Serial_SendString(debug_serial1_buf);

							// 添加到x缓存（图形尺寸数据）
							add_x_sample(current_shape_size);

							// 只有按键按下后才更新显示
							if (button_pressed) {
								UpdateHMIDisplay();
							}
						}
					}
					else if (strncmp(&line_data[1], "SQUARE:", 7) == 0)
					{
						if (sscanf(&line_data[8], "%f", &current_shape_size) == 1)
						{
							strcpy(current_shape_type, "正方形");
							sprintf(debug_serial1_buf, "接收正方形数据: 边长 %.2f cm\r\n", current_shape_size);
							Serial_SendString(debug_serial1_buf);

							// 添加到x缓存（图形尺寸数据）
							add_x_sample(current_shape_size);

							// 只有按键按下后才更新显示
							if (button_pressed) {
								UpdateHMIDisplay();
							}
						}
					}
					else if (strncmp(&line_data[1], "NO_DETECTION:", 13) == 0)
					{
						strcpy(current_shape_type, "无检测");
						current_distance = 0.0f;
						current_shape_size = 0.0f;
						Serial_SendString("摄像头无检测数据\r\n");

						// 清零显示
						Serial2_SendString("index.x2.val=0\xff\xff\xff");
						Delay_ms(10);
						Serial2_SendString("index.x3.val=0\xff\xff\xff");
						Delay_ms(10);
					}
					else
					{
						// 兼容原有的纯数字格式（如@123.45）
						float camera_data = 0.0f;
						if(sscanf(&line_data[1],"%f",&camera_data)==1)
						{
							current_distance = camera_data;
							current_shape_size = 0.0f;
							strcpy(current_shape_type, "距离");
							sprintf(debug_serial1_buf, "接收距离数据(旧格式): %.2f cm\r\n", camera_data);
							Serial_SendString(debug_serial1_buf);

							// 添加到缓存
							add_distance_sample(current_distance);

							// 只有按键按下后才更新显示
							if (button_pressed) {
								UpdateHMIDisplay();
							}
						}
						else
						{
							Serial_SendString("错误: 无法解析摄像头数据\r\n");
						}
					}
				}
				// 处理以#开头的数据（如#123.45）
				else if (line_data[0] == '#')
				{
					float hash_data = 0.0f;
					if(sscanf(&line_data[1], "%f", &hash_data) == 1)
					{
						x = hash_data;  // 将解析出的数据赋给x变量
						sprintf(debug_serial1_buf, "接收#数据: %.2f，赋值给x变量\r\n", x);
						Serial_SendString(debug_serial1_buf);

						// 添加到缓存
						add_x_sample(x);

						// 只有按键按下后才更新显示
						if (button_pressed) {
							UpdateHMIDisplay();
						}
					}
					else
					{
						Serial_SendString("错误: 无法解析#数据\r\n");
					}
				}
			}
		}

		// 检查串口2接收（HMI按键数据）
		if(Serial2_GetLineFlag() == 1) // 检查是否接收到完整行
		{
			Serial2_ClearLineFlag(); // 清除标志位
			Serial_SendString("=== RX from HMI ===\r\n");
			Serial_SendString("Raw data: ");
			char* hmi_data = Serial2_GetLineData(); // 获取HMI数据
			Serial_SendString(hmi_data);
			Serial_SendString("\r\n");

			uint8_t hmi_data_length = strlen(hmi_data);
			sprintf(debug_serial1_buf, "HMI Data length: %d\r\n", hmi_data_length);
			Serial_SendString(debug_serial1_buf);

			// 处理HMI按键发送的数据（格式：@1\n）
			if (hmi_data_length > 0 && hmi_data[0] == '@')
			{
				if (hmi_data[1] == '1')
				{
					Serial_SendString("检测到按键按下信号！\r\n");
					handle_button_press();
				}
				else
				{
					sprintf(debug_serial1_buf, "未知HMI命令: %c\r\n", hmi_data[1]);
					Serial_SendString(debug_serial1_buf);
				}
			}
			else
			{
				Serial_SendString("HMI数据格式错误\r\n");
			}
		}

		Delay_ms(10); // 减少延时，提高响应速度
	}
}